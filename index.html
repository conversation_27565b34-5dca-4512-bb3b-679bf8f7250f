<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link href="https://fonts.cdnfonts.com/css/satoshi" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reset-css@5.0.2/reset.min.css">
  <link rel="stylesheet" href="style2.css">
  <style>
    @font-face {
      font-family: 'Retrips';
      src: url('./Retripsdemo-gxMRE.otf') format('opentype');
      font-weight: normal;
      font-style: normal;
    }

    :root {
      --primary-color: #FD4561;
      --secondary-color: #E2D7F7;
      --accent-color: #FD4561;
      --black: #101828;
      --black-p: #667085;
      --gray: #F9FAFB;
      --n-gray: #F2F4F7;
    }

    /* Reset basic elements */
    body,
    header,
    nav,
    ul,
    li,
    a {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: '<PERSON>shi', sans-serif;



    }

    /* Header container */
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 2rem;
      background-color: #ffffff;
      border-bottom: 1px solid var(--n-gray);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* SVG icon (e.g., search icon) */
    header .icon {
      width: 24px;
      height: 24px;
      fill: #6b7280;
      cursor: pointer;
      margin-right: 1.5rem;
    }

    /* Navigation bar */

    header nav ul {
      display: flex;
      align-items: center;
      justify-content: space-between;
      list-style: none;
      gap: 1rem;
    }

    .block-col {
      flex-direction: column;
    }
    .footer-container{
      display: flex;
    }
    /* Navigation links */
    header nav ul li a {
      text-decoration: none;
      color: #374151;
      font-size: 0.95rem;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    header nav ul li a:hover {
      color: #4f46e5;
    }

    /* Brand section (logo + name) */
    header nav ul li.brand {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0px 20px;
    }

    header nav ul li.brand a {
      display: flex;
      align-items: center;
      font-size: 1.4rem;
      font-weight: 600;
      color: #111827;
      text-decoration: none;
      gap: 0.5rem;
    }

    header nav ul li.brand img {
      height: 36px;
      width: auto;
    }
    .bg-gray{
       background: #f9fafb;
    }
    .hero {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4rem 2rem;
      gap: 2rem;
      min-height: 380px;
    }

    /* Left Side */
    .hero .block {
      max-width: 50%;
    }

    .hero .block h1 {
      font-size: 2.75rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: #1f2937;
      line-height: 1.2;
    }

    .hero .block p {
      font-size: 1.125rem;
      color: var(--black-p);
      margin-bottom: 2rem;
    }

    .hero .block button {
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      font-weight: 600;
      background-color: var(--accent-color);
      color: #fff;
      border: none;
      border-radius: 0.5rem;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .hero .block button:hover {
      background-color: #2563eb;
    }

    /* Right Side */
    .hero .block {
      height: 320px;
      display: flex;
      align-items: flex-start;
    }

    .block-gap-40 {
      gap: 40px;
    }

    .block-gap-2 {
      gap: 2rem;
    }

    .block-gap-1 {
      gap: 1rem;
    }

    /* AR Card */
    .hero .ar-card {
      height: 380px;
      width: 280px;
      border-radius: 1rem;
      text-align: center;
      display: flex;
      flex-direction: column;
    }

    .hero .ar-card .image-container {
      background-color: var(--secondary-color);
      width: 100%;
      max-width: 300px;
      height: 300px;
      margin: 0 auto 1rem;
      padding: 10px;
      border-radius: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .hero .ar-card img {
      width: 100%;
      height: auto;
      display: block;
    }

    .hero .ar-card p {
      color: #374151;
      font-size: 1rem;
      line-height: 1.5;
    }

    .hero .ar-card span {
      color: var(--accent-color);
      font-weight: 600;
    }

    /* Girl Card */
    .hero .girl-card {
      height: 380px;
    width: 280px;
    position: relative;
    text-align: center;
    background: var(--secondary-color);
    padding-top: 1rem;
    display: flex;
    border-radius: 1rem;
    flex-direction: column;
    justify-content: space-between;
    }

    .hero .girl-card h4 {
      font-family: 'Retrips', sans-serif;
      font-size: 1.95rem;
      font-weight: 700;
      color: #FD4561;
      margin-bottom: 0.75rem;
    }

    span.highlight {
      color: #FD4561;
      font-weight: 900;
    }

    .hero .girl-card img {
      height: 270px;
      width: 280px;
      border-radius: 0.5rem;
    }

    /* Responsive */
    @media (max-width: 900px) {
      .hero {
        flex-direction: column;
        text-align: center;
      }

      .hero .block,
      .hero .right {
        max-width: 100%;
      }
    }

    .features {
      padding: 4rem 2rem;
    }

    .features h3 {
      font-size: 3.5rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 1rem;
    }

    .features p {
      font-size: 1.125rem;
      color: var(--black-p);
      margin-bottom: 2rem;
    }

    .features .f_cards {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
    }

    .features .f_cards .f_card {
      padding: 30px;
      background: linear-gradient(135deg, #F9FAFB, #E2D7F7);
      border-radius: 35px;
      margin: 14px;
      flex: 1 1 280px;
    }

    .features .f_cards .f_card svg {
      width: 50px;
      height: 110px;

    }

    .features .f_cards .f_card h4 {
      font-weight: 600;
      font-size: 24px;
    }

    .features .f_cards .f_card p {
      font-size: 18px;
      margin-top: 10px;
    }

    .block-item {
      display: flex;
      align-items: flex-start;
      justify-content: center;
    }

    .block-item:nth-child(2) {
      margin-top: -145px;
      align-items: flex-end;
    }

    .block-item:nth-child(2) img {
      background: #B39DDB;
      border-radius: 10px;
    }

    .testimonals {
      padding: 4rem 2rem;
      background: var(--accent-color);
      color: #fff;
      display: flex;
      align-items: center;
      flex-direction: column;
    }

    .testimonals h3 {
      font-size: 20px;
      font-weight: 600;
    }

    .testimonals p {
      font-size: 18px;
      margin-top: 10px;
    }

    .t_cards {
      margin-top: 20px;
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
    }

    .t_card {
      flex: 0 1 320px;
      background: #fff;
      border-radius: 10px;
      padding: 20px;
      color: #000;
    }

    .t_card img {
      background: #B39DDB;
      border-radius: 50%;
    }

    .t_card .t_card-header {
      display: flex;
      align-items: center;
      gap: 11px;
      font-weight: 500;
    }

    .t_card p {
      font-size: 14px;
      line-height: 1.5;
    }

    .container {
      max-width: 1080px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .links {
        display: flex;
    gap: 12px;
    flex-direction: column;
    }

    .links svg {
      width: 20px;
      height: 20px;
    }

    .links .link {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .links .link p {
      margin: 0;
    }
    .contact-form {
      max-width: 600px;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.form-input,
.form-textarea {
  padding: 0.75rem 1rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 1rem;
  flex: 1;
  min-width: 0;
}

.full-width {
  width: 100%;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-submit-btn {
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  align-self: flex-start;
  transition: background-color 0.2s ease;
}

.form-submit-btn:hover {
  background-color: #4338ca;
}
.site-footer {
display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 3rem 2rem;
    color: var(--black);
    font-family: 'Inter', sans-serif;
    gap: 2rem;
    flex-direction: column;
}

.footer-col {
  flex: 1 1 200px;
  min-width: 180px;
}

.logo-col {
  flex: 2 1 300px;
}

.logo-data {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-data img {
  height: 40px;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 600;
}

.col-title {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--black);
}

.links a,
.info-links a {
  display: block;
    color: var(--black);
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.2s ease;
}

.links a:hover,
.info-links a:hover {
    color: var(--black);
}

.address div,
.info-links div {
  margin-bottom: 0.5rem;
     color: var(--black);
}

@media (max-width: 768px) {
  .site-footer {
    flex-direction: column;
    gap: 2rem;
  }

  .logo-data {
    justify-content: center;
  }

  .footer-col {
    text-align: center;
  }
}

.social-links{
  display: flex;
    gap: 1.25rem;
    justify-content: flex-end;
}
.social-links svg{
   width: 30px;
  height: 30px;
}
.copy-right{
    color: #667085;
    width: 100%;
    text-align: center;
}
.address p{
  margin-bottom: 14px;
}
  </style>
</head>

<body>


  <header>
    <svg class="icon">
      <use xlink:href="#SEARCH"></use>
    </svg>

    <nav>
      <ul>
        <li><a href="#">Home</a></li>
        <li><a href="#">Services</a></li>
        <li><a href="#">About Us</a></li>
        <li class="brand">
          <a href="#">
            <img src="./Logo.svg">
            VisionVitality
          </a>
        </li>
        <li><a href="#">Contact</a></li>
        <li><a href="#">Blog</a></li>
        <li><a href="#">Testimonals</a></li>
      </ul>
    </nav>
    <svg class="icon">
      <use xlink:href="#CART"></use>
    </svg>
  </header>
  <main>
    <section class="hero bg-gray">
      <div class="block block-col">
        <h1>Clear Vision for a Bright Future</h1>
        <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
        <button>Book an Appointment</button>
      </div>
      <div class="block  block-gap-40">
        <div class="ar-card">
          <div class="image-container">
            <img src="https://cdn.faire.com/fastly/d7ff44cc7f6d748ebe285ec2b434c49f958106f5d34e0daa9af1df416d255146.png?dpr=1&fit=crop&format=jpg&height=1440&width=1440" />
          </div>
          <p>
            Restore the missing <span>confidence</span> back to your daily life again.
          </p>
        </div>
        <div class="girl-card">
          <h4>RESTORE THE CONFIDENCE</h4>
          <img src="girl.png" />
        </div>
      </div>
    </section>
    <section class="features">
      <h3>Features</h3>
      <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
      <div class="f_cards">
        <div class="f_card">
          <svg class="icon">
            <use xlink:href="#glass"></use>
          </svg>
          <h4>Advanced Technology</h4>
          <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
        </div>
        <div class="f_card">
          <svg class="icon">
            <use xlink:href="#glass"></use>
          </svg>
          <h4>Advanced Technology</h4>
          <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
        </div>
        <div class="f_card">
          <svg class="icon">
            <use xlink:href="#glass"></use>
          </svg>
          <h4>Advanced Technology</h4>
          <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
        </div>
        <div class="f_card">
          <svg class="icon">
            <use xlink:href="#glass"></use>
          </svg>
          <h4>Advanced Technology</h4>
          <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
        </div>
        <div class="f_card">
          <svg class="icon">
            <use xlink:href="#glass"></use>
          </svg>
          <h4>Advanced Technology</h4>
          <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
        </div>
      </div>
    </section>
    <section class="hero">
      <div class="block block-col">
        <div class="block-item block-gap-40">
          <img src="./g1.png" />
          <p>With <span class="highlight"> 20% </span>Off for your<br> first purchase</p>
        </div>
        <div class="block-item block-gap-40">
          <p> More than <span class="highlight">10000</span> <br>happy customers</p>
          <img src="./g2.png" />
        </div>
      </div>
      <div class="block block-col">
        <h1>Embrace Life with Crystal-Clear Vision</h1>
        <p>Enhance your visual experience with our cutting-edge eyewear collection, meticulously curated for both style
          and comfort.</p>
        <button>Order Now</button>
      </div>

    </section>
    <section class="testimonals">
      <h3>What Our Patients Say</h3>
      <p>Discover a world of vibrant colors and crisp clarity with our comprehensive eye care</p>
      <div class="t_cards">
        <div class="t_card">
          <div class="t_card-header">
            <img src="./TESTIMONAL_GRL.svg" />
            <h4>Sara H</h4>
          </div>
          <p>VisionVitality truly transformed my outlook on life. Their compassionate care and expertise helped me
            regain my confidence in my vision.</p>
        </div>
        <div class="t_card">
          <div class="t_card-header">
            <img src="./TESTIMONAL_GRL.svg" />
            <h4>Sara H</h4>
          </div>
          <p>VisionVitality truly transformed my outlook on life. Their compassionate care and expertise helped me
            regain my confidence in my vision.</p>
        </div>
        <div class="t_card">
          <div class="t_card-header">
            <img src="./TESTIMONAL_GRL.svg" />
            <h4>Sara H</h4>
          </div>
          <p>VisionVitality truly transformed my outlook on life. Their compassionate care and expertise helped me
            regain my confidence in my vision.</p>
        </div>
        <div class="t_card">
          <div class="t_card-header">
            <img src="./TESTIMONAL_GRL.svg" />
            <h4>Sara H</h4>
          </div>
          <p>VisionVitality truly transformed my outlook on life. Their compassionate care and expertise helped me
            regain my confidence in my vision.</p>
        </div>
      </div>
    </section>
    <section class="hero">
      <div class="container">
        <div class="block block-col">
          <h1>Protect Your Vision, Embrace the Outdoors</h1>
          <p>Explore our collection of protective eyewear designed to shield your eyes from harmful UV rays, ensuring
            optimal visual health in any environment.</p>
          <button>Order Now</button>
        </div>
        <div class="block">
          <img src="./g1.png" alt="">
        </div>
      </div>
    </section>
    <section class="hero bg-gray">
      <div class="block block-col">
        <h1>Reach Out for Personalized Care</h1>
        <p>Have questions or want to schedule an appointment? Fill out the form below, and our team will get back to you
          promptly..</p>
        <div class="links">
          <div class="link">
            <svg class="icon">
              <use xlink:href="#MAIL"></use>
            </svg>
            <p><EMAIL></p>
          </div>
          <div class="link">
            <svg class="icon">
              <use xlink:href="#CALL"></use>
            </svg>
            <p>(907) 789-7623</p>
          </div>
        </div>
      </div>
      <div class="block">
      <form class="contact-form">
        <div class="form-row">
          <input type="text" id="f_name" name="f_name" class="form-input" placeholder="First Name" required>
          <input type="text" id="l_name" name="l_name" class="form-input" placeholder="Last Name" required>
        </div>

        <div class="form-row">
          <input type="email" id="email" name="email" class="form-input full-width" placeholder="Email" required>
        </div>

        <div class="form-row">
          <textarea rows="5" class="form-textarea full-width" placeholder="Message" required></textarea>
        </div>

        <button class="form-submit-btn">Submit</button>
      </form>

      </div>
    </section>
  </main>
<footer class="site-footer">
  <div class="footer-container">
      <div class="footer-col logo-col">
    <div class="logo-data"> 
      <img src="./Logo.svg" alt="Logo">
      <span class="brand-name">VisionVitality</span>
    </div>
  </div>
  <div class="footer-col">
    <div class="col-title">Pages</div>
    <div class="links">
      <a href="#">Home</a>
      <a href="#">Services</a>
      <a href="#">About Us</a>
      <a href="#">Contact</a>
      <a href="#">Blog</a>
       <a href="#">Testimonals</a>
    </div>
  </div>
  <div class="footer-col">
    <div class="col-title">Contact</div>
    <div class="address">
      <p>9153 Jerry Dr, Juneau, Alaska 99801, USA</p>
      <p>(907) 789-7623</p>
      <p><EMAIL></p>
    </div>
  </div>
  <div class="footer-col">
    <div class="col-title">Legal</div>
    <div class="info-links">
      <a href="#">Terms of Use</a>
      <a href="#">Privacy Policy</a>
    </div>
  </div>
  </div>
  <div class="social-links">
     <svg class="icon">
          <use xlink:href="#DISCORD"></use>
      </svg>
        <svg class="icon">
          <use xlink:href="#INSTA"></use>
      </svg>
      
        <svg class="icon">
          <use xlink:href="#MAIL"></use>
      </svg>
        <svg class="icon">
          <use xlink:href="#TELEGRAM"></use>
      </svg>
        <svg class="icon">
          <use xlink:href="#TWITTER"></use>
      </svg>
       <svg class="icon">
          <use xlink:href="#YTUBE"></use>
      </svg>
      
  
  
  
  
  
  </div>
  <div class="copy-right">
    <p>© 2023 VisionVitality. All rights reserved.</p>
  </div>


</footer>




  <svg width="0" height="0" class="hidden">
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="CALL">
      <path
        d="M17.62 10.75C17.19 10.75 16.85 10.4 16.85 9.97998C16.85 9.60998 16.48 8.83998 15.86 8.16998C15.25 7.51998 14.58 7.13998 14.02 7.13998C13.59 7.13998 13.25 6.78998 13.25 6.36998C13.25 5.94998 13.6 5.59998 14.02 5.59998C15.02 5.59998 16.07 6.13998 16.99 7.10998C17.85 8.01998 18.4 9.14998 18.4 9.96998C18.4 10.4 18.05 10.75 17.62 10.75Z"
        fill="#FD4561"></path>
      <path
        d="M21.23 10.75C20.8 10.75 20.46 10.4 20.46 9.98C20.46 6.43 17.57 3.55 14.03 3.55C13.6 3.55 13.26 3.2 13.26 2.78C13.26 2.36 13.6 2 14.02 2C18.42 2 22 5.58 22 9.98C22 10.4 21.65 10.75 21.23 10.75Z"
        fill="#FD4561"></path>
      <path
        d="M11.05 14.95L9.2 16.8C8.81 17.19 8.19 17.19 7.79 16.81C7.68 16.7 7.57 16.6 7.46 16.49C6.43 15.45 5.5 14.36 4.67 13.22C3.85 12.08 3.19 10.94 2.71 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C10.83 13.32 10.94 13.42 11.04 13.52C11.44 13.91 11.45 14.55 11.05 14.95Z"
        fill="#FD4561"></path>
      <path
        d="M21.9701 18.33C21.9701 18.61 21.9201 18.9 21.8201 19.18C21.7901 19.26 21.7601 19.34 21.7201 19.42C21.5501 19.78 21.3301 20.12 21.0401 20.44C20.5501 20.98 20.0101 21.37 19.4001 21.62C19.3901 21.62 19.3801 21.63 19.3701 21.63C18.7801 21.87 18.1401 22 17.4501 22C16.4301 22 15.3401 21.76 14.1901 21.27C13.0401 20.78 11.8901 20.12 10.7501 19.29C10.3601 19 9.9701 18.71 9.6001 18.4L12.8701 15.13C13.1501 15.34 13.4001 15.5 13.6101 15.61C13.6601 15.63 13.7201 15.66 13.7901 15.69C13.8701 15.72 13.9501 15.73 14.0401 15.73C14.2101 15.73 14.3401 15.67 14.4501 15.56L15.2101 14.81C15.4601 14.56 15.7001 14.37 15.9301 14.25C16.1601 14.11 16.3901 14.04 16.6401 14.04C16.8301 14.04 17.0301 14.08 17.2501 14.17C17.4701 14.26 17.7001 14.39 17.9501 14.56L21.2601 16.91C21.5201 17.09 21.7001 17.3 21.8101 17.55C21.9101 17.8 21.9701 18.05 21.9701 18.33Z"
        fill="#FD4561"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="CART">
      <path
        d="M5.18988 6.37994C4.99988 6.37994 4.79988 6.29994 4.65988 6.15994C4.36988 5.86994 4.36988 5.38994 4.65988 5.09994L8.28988 1.46994C8.57988 1.17994 9.05988 1.17994 9.34988 1.46994C9.63988 1.75994 9.63988 2.23994 9.34988 2.52994L5.71988 6.15994C5.56988 6.29994 5.37988 6.37994 5.18988 6.37994Z"
        fill="#101828"></path>
      <path
        d="M18.8101 6.37994C18.6201 6.37994 18.4301 6.30994 18.2801 6.15994L14.6501 2.52994C14.3601 2.23994 14.3601 1.75994 14.6501 1.46994C14.9401 1.17994 15.4201 1.17994 15.7101 1.46994L19.3401 5.09994C19.6301 5.38994 19.6301 5.86994 19.3401 6.15994C19.2001 6.29994 19.0001 6.37994 18.8101 6.37994Z"
        fill="#101828"></path>
      <path
        d="M20.21 10.6001C20.14 10.6001 20.07 10.6001 20 10.6001H19.77H4C3.3 10.6101 2.5 10.6101 1.92 10.0301C1.46 9.5801 1.25 8.8801 1.25 7.8501C1.25 5.1001 3.26 5.1001 4.22 5.1001H19.78C20.74 5.1001 22.75 5.1001 22.75 7.8501C22.75 8.8901 22.54 9.5801 22.08 10.0301C21.56 10.5501 20.86 10.6001 20.21 10.6001ZM4.22 9.1001H20.01C20.46 9.1101 20.88 9.1101 21.02 8.9701C21.09 8.9001 21.24 8.6601 21.24 7.8501C21.24 6.7201 20.96 6.6001 19.77 6.6001H4.22C3.03 6.6001 2.75 6.7201 2.75 7.8501C2.75 8.6601 2.91 8.9001 2.97 8.9701C3.11 9.1001 3.54 9.1001 3.98 9.1001H4.22Z"
        fill="#101828"></path>
      <path
        d="M9.75977 18.3C9.34977 18.3 9.00977 17.96 9.00977 17.55V14C9.00977 13.59 9.34977 13.25 9.75977 13.25C10.1698 13.25 10.5098 13.59 10.5098 14V17.55C10.5098 17.97 10.1698 18.3 9.75977 18.3Z"
        fill="#101828"></path>
      <path
        d="M14.3599 18.3C13.9499 18.3 13.6099 17.96 13.6099 17.55V14C13.6099 13.59 13.9499 13.25 14.3599 13.25C14.7699 13.25 15.1099 13.59 15.1099 14V17.55C15.1099 17.97 14.7699 18.3 14.3599 18.3Z"
        fill="#101828"></path>
      <path
        d="M14.8902 22.75H8.86024C5.28024 22.75 4.48024 20.62 4.17024 18.77L2.76024 10.12C2.69024 9.71 2.97024 9.33 3.38024 9.26C3.79024 9.19 4.17024 9.47 4.24024 9.88L5.65024 18.52C5.94024 20.29 6.54024 21.25 8.86024 21.25H14.8902C17.4602 21.25 17.7502 20.35 18.0802 18.61L19.7602 9.86C19.8402 9.45 20.2302 9.18 20.6402 9.27C21.0502 9.35 21.3102 9.74 21.2302 10.15L19.5502 18.9C19.1602 20.93 18.5102 22.75 14.8902 22.75Z"
        fill="#101828"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="DISCORD">
      <g clip-path="url(#clip0_2309_221)">
        <path
          d="M18.789 6.25623C17.4093 5.66091 15.9528 5.23857 14.4566 5C14.2518 5.34494 14.0666 5.69983 13.9016 6.0632C12.3079 5.83686 10.6871 5.83686 9.09342 6.0632C8.92835 5.69987 8.7431 5.34498 8.53845 5C7.0413 5.24059 5.58379 5.66393 4.20267 6.25934C1.46081 10.0825 0.71754 13.8107 1.08918 17.486C2.69488 18.6041 4.49212 19.4544 6.40277 20C6.83299 19.4547 7.21368 18.8761 7.5408 18.2705C6.91948 18.0518 6.3198 17.782 5.74869 17.4642C5.89899 17.3615 6.046 17.2556 6.18804 17.1529C7.84984 17.8894 9.6636 18.2713 11.5 18.2713C13.3364 18.2713 15.1501 17.8894 16.8119 17.1529C16.9556 17.2634 17.1026 17.3692 17.2513 17.4642C16.6791 17.7825 16.0783 18.0529 15.4559 18.2721C15.7826 18.8774 16.1633 19.4555 16.5939 20C18.5062 19.4566 20.3048 18.6067 21.9108 17.4875C22.3468 13.2254 21.1659 9.53144 18.789 6.25623ZM8.01155 15.2257C6.97592 15.2257 6.12032 14.34 6.12032 13.2503C6.12032 12.1606 6.94619 11.2671 8.00824 11.2671C9.0703 11.2671 9.91929 12.1606 9.90112 13.2503C9.88295 14.34 9.067 15.2257 8.01155 15.2257ZM14.9884 15.2257C13.9511 15.2257 13.0989 14.34 13.0989 13.2503C13.0989 12.1606 13.9247 11.2671 14.9884 11.2671C16.0521 11.2671 16.8945 12.1606 16.8763 13.2503C16.8582 14.34 16.0439 15.2257 14.9884 15.2257Z"
          fill="#FD4561"></path>
      </g>
      <defs>
        <clipPath id="clip0_2309_221">
          <rect width="21" height="15" fill="white" transform="translate(1 5)"></rect>
        </clipPath>
      </defs>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="glass">
      <path
        d="M17.71 2H16.76C16.37 2 16.05 2.32 16.05 2.71C16.05 3.11 16.37 3.43 16.76 3.43H17.71C19.42 3.43 20.81 4.82 20.81 6.52V12.4C20.45 12.21 20.05 12.11 19.62 12.11H15.81C14.37 12.11 13.19 13.28 13.19 14.73V16.29H10.81V14.73C10.81 13.28 9.63001 12.11 8.19001 12.11H4.38001C3.95001 12.11 3.55001 12.21 3.19001 12.4V6.52C3.19001 4.82 4.58001 3.43 6.29001 3.43H7.24001C7.63001 3.43 7.95001 3.11 7.95001 2.71C7.95001 2.32 7.63001 2 7.24001 2H6.29001C3.79001 2 1.76001 4.03 1.76001 6.52V14.73V19.38C1.76001 20.83 2.94001 22 4.38001 22H8.19001C9.63001 22 10.81 20.83 10.81 19.38V17.71H13.19V19.38C13.19 20.83 14.37 22 15.81 22H19.62C21.06 22 22.24 20.83 22.24 19.38V14.73V6.52C22.24 4.03 20.21 2 17.71 2Z"
        fill="#101828"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="INSTA">
      <path
        d="M12 2.16094C15.2063 2.16094 15.5859 2.175 16.8469 2.23125C18.0188 2.28281 18.6516 2.47969 19.0734 2.64375C19.6313 2.85938 20.0344 3.12188 20.4516 3.53906C20.8734 3.96094 21.1313 4.35938 21.3469 4.91719C21.5109 5.33906 21.7078 5.97656 21.7594 7.14375C21.8156 8.40937 21.8297 8.78906 21.8297 11.9906C21.8297 15.1969 21.8156 15.5766 21.7594 16.8375C21.7078 18.0094 21.5109 18.6422 21.3469 19.0641C21.1313 19.6219 20.8687 20.025 20.4516 20.4422C20.0297 20.8641 19.6313 21.1219 19.0734 21.3375C18.6516 21.5016 18.0141 21.6984 16.8469 21.75C15.5813 21.8062 15.2016 21.8203 12 21.8203C8.79375 21.8203 8.41406 21.8062 7.15313 21.75C5.98125 21.6984 5.34844 21.5016 4.92656 21.3375C4.36875 21.1219 3.96563 20.8594 3.54844 20.4422C3.12656 20.0203 2.86875 19.6219 2.65313 19.0641C2.48906 18.6422 2.29219 18.0047 2.24063 16.8375C2.18438 15.5719 2.17031 15.1922 2.17031 11.9906C2.17031 8.78438 2.18438 8.40469 2.24063 7.14375C2.29219 5.97187 2.48906 5.33906 2.65313 4.91719C2.86875 4.35938 3.13125 3.95625 3.54844 3.53906C3.97031 3.11719 4.36875 2.85938 4.92656 2.64375C5.34844 2.47969 5.98594 2.28281 7.15313 2.23125C8.41406 2.175 8.79375 2.16094 12 2.16094ZM12 0C8.74219 0 8.33438 0.0140625 7.05469 0.0703125C5.77969 0.126563 4.90313 0.332812 4.14375 0.628125C3.35156 0.9375 2.68125 1.34531 2.01563 2.01562C1.34531 2.68125 0.9375 3.35156 0.628125 4.13906C0.332812 4.90313 0.126563 5.775 0.0703125 7.05C0.0140625 8.33437 0 8.74219 0 12C0 15.2578 0.0140625 15.6656 0.0703125 16.9453C0.126563 18.2203 0.332812 19.0969 0.628125 19.8563C0.9375 20.6484 1.34531 21.3188 2.01563 21.9844C2.68125 22.65 3.35156 23.0625 4.13906 23.3672C4.90313 23.6625 5.775 23.8687 7.05 23.925C8.32969 23.9812 8.7375 23.9953 11.9953 23.9953C15.2531 23.9953 15.6609 23.9812 16.9406 23.925C18.2156 23.8687 19.0922 23.6625 19.8516 23.3672C20.6391 23.0625 21.3094 22.65 21.975 21.9844C22.6406 21.3188 23.0531 20.6484 23.3578 19.8609C23.6531 19.0969 23.8594 18.225 23.9156 16.95C23.9719 15.6703 23.9859 15.2625 23.9859 12.0047C23.9859 8.74688 23.9719 8.33906 23.9156 7.05938C23.8594 5.78438 23.6531 4.90781 23.3578 4.14844C23.0625 3.35156 22.6547 2.68125 21.9844 2.01562C21.3188 1.35 20.6484 0.9375 19.8609 0.632812C19.0969 0.3375 18.225 0.13125 16.95 0.075C15.6656 0.0140625 15.2578 0 12 0Z"
        fill="#FD4561"></path>
      <path
        d="M12 5.83594C8.59688 5.83594 5.83594 8.59688 5.83594 12C5.83594 15.4031 8.59688 18.1641 12 18.1641C15.4031 18.1641 18.1641 15.4031 18.1641 12C18.1641 8.59688 15.4031 5.83594 12 5.83594ZM12 15.9984C9.79219 15.9984 8.00156 14.2078 8.00156 12C8.00156 9.79219 9.79219 8.00156 12 8.00156C14.2078 8.00156 15.9984 9.79219 15.9984 12C15.9984 14.2078 14.2078 15.9984 12 15.9984Z"
        fill="#FD4561"></path>
      <path
        d="M19.8469 5.59214C19.8469 6.38902 19.2 7.0312 18.4078 7.0312C17.6109 7.0312 16.9688 6.38433 16.9688 5.59214C16.9688 4.79526 17.6156 4.15308 18.4078 4.15308C19.2 4.15308 19.8469 4.79995 19.8469 5.59214Z"
        fill="#FD4561"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="MAIL">
      <path
        d="M17 3.5H7C4 3.5 2 5 2 8.5V15.5C2 19 4 20.5 7 20.5H17C20 20.5 22 19 22 15.5V8.5C22 5 20 3.5 17 3.5ZM17.47 9.59L14.34 12.09C13.68 12.62 12.84 12.88 12 12.88C11.16 12.88 10.31 12.62 9.66 12.09L6.53 9.59C6.21 9.33 6.16 8.85 6.41 8.53C6.67 8.21 7.14 8.15 7.46 8.41L10.59 10.91C11.35 11.52 12.64 11.52 13.4 10.91L16.53 8.41C16.85 8.15 17.33 8.2 17.58 8.53C17.84 8.85 17.79 9.33 17.47 9.59Z"
        fill="#FD4561"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="SEARCH">
      <path
        d="M11.5 21.75C5.85 21.75 1.25 17.15 1.25 11.5C1.25 5.85 5.85 1.25 11.5 1.25C17.15 1.25 21.75 5.85 21.75 11.5C21.75 17.15 17.15 21.75 11.5 21.75ZM11.5 2.75C6.67 2.75 2.75 6.68 2.75 11.5C2.75 16.32 6.67 20.25 11.5 20.25C16.33 20.25 20.25 16.32 20.25 11.5C20.25 6.68 16.33 2.75 11.5 2.75Z"
        fill="#101828"></path>
      <path
        d="M21.9999 22.7499C21.8099 22.7499 21.6199 22.6799 21.4699 22.5299L19.4699 20.5299C19.1799 20.2399 19.1799 19.7599 19.4699 19.4699C19.7599 19.1799 20.2399 19.1799 20.5299 19.4699L22.5299 21.4699C22.8199 21.7599 22.8199 22.2399 22.5299 22.5299C22.3799 22.6799 22.1899 22.7499 21.9999 22.7499Z"
        fill="#101828"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="TELEGRAM">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12ZM12.4292 8.85905C11.262 9.34452 8.92928 10.3493 5.43104 11.8734C4.86298 12.0993 4.5654 12.3203 4.53831 12.5364C4.49253 12.9016 4.94986 13.0454 5.57262 13.2412C5.65733 13.2679 5.7451 13.2955 5.83508 13.3247C6.44778 13.5239 7.27197 13.7569 7.70043 13.7662C8.08909 13.7745 8.52287 13.6143 9.00179 13.2855C12.2703 11.0791 13.9575 9.96393 14.0635 9.93989C14.1382 9.92293 14.2417 9.9016 14.3119 9.96397C14.3821 10.0263 14.3752 10.1444 14.3677 10.1761C14.3224 10.3693 12.5273 12.0382 11.5983 12.9019C11.3086 13.1712 11.1032 13.3621 11.0612 13.4058C10.9671 13.5035 10.8713 13.5959 10.7791 13.6847C10.2099 14.2335 9.78306 14.6449 10.8028 15.3169C11.2928 15.6398 11.6849 15.9069 12.0761 16.1732C12.5033 16.4642 12.9294 16.7544 13.4808 17.1158C13.6212 17.2079 13.7554 17.3035 13.886 17.3966C14.3832 17.7511 14.8299 18.0695 15.3817 18.0188C15.7024 17.9892 16.0336 17.6877 16.2018 16.7885C16.5994 14.6633 17.3808 10.0586 17.5614 8.1611C17.5772 7.99485 17.5573 7.78209 17.5413 7.6887C17.5253 7.5953 17.4919 7.46223 17.3705 7.36372C17.2267 7.24706 17.0048 7.22246 16.9055 7.22421C16.4542 7.23216 15.7617 7.47294 12.4292 8.85905Z"
        fill="#FD4561"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 20" id="TWITTER">
      <path
        d="M7.55016 19.75C16.6045 19.75 21.5583 12.2467 21.5583 5.74186C21.5583 5.53092 21.5536 5.3153 21.5442 5.10436C22.5079 4.40746 23.3395 3.54425 24 2.5553C23.1025 2.9546 22.1496 3.21538 21.1739 3.32874C22.2013 2.71291 22.9705 1.74547 23.3391 0.605767C22.3726 1.17856 21.3156 1.58261 20.2134 1.80061C19.4708 1.01156 18.489 0.489116 17.4197 0.314051C16.3504 0.138986 15.2532 0.32105 14.2977 0.832096C13.3423 1.34314 12.5818 2.15471 12.1338 3.14131C11.6859 4.12792 11.5754 5.23462 11.8195 6.2903C9.86249 6.19209 7.94794 5.6837 6.19998 4.7981C4.45203 3.91249 2.90969 2.66944 1.67297 1.14952C1.0444 2.23324 0.852057 3.51565 1.13503 4.73609C1.418 5.95654 2.15506 7.02345 3.19641 7.71999C2.41463 7.69517 1.64998 7.48468 0.965625 7.10592V7.16686C0.964925 8.30415 1.3581 9.40659 2.07831 10.2868C2.79852 11.167 3.80132 11.7706 4.91625 11.995C4.19206 12.1931 3.43198 12.222 2.69484 12.0794C3.00945 13.0574 3.62157 13.9129 4.44577 14.5263C5.26997 15.1398 6.26512 15.4806 7.29234 15.5012C5.54842 16.8711 3.39417 17.6141 1.17656 17.6106C0.783287 17.61 0.390399 17.5859 0 17.5384C2.25286 18.9837 4.87353 19.7514 7.55016 19.75Z"
        fill="#FD4561"></path>
    </symbol>
    <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 18" id="YTUBE">
      <path
        d="M23.7609 4.20005C23.7609 4.20005 23.5266 2.54536 22.8047 1.8188C21.8906 0.862549 20.8688 0.857861 20.4 0.801611C17.0438 0.557861 12.0047 0.557861 12.0047 0.557861H11.9953C11.9953 0.557861 6.95625 0.557861 3.6 0.801611C3.13125 0.857861 2.10938 0.862549 1.19531 1.8188C0.473438 2.54536 0.24375 4.20005 0.24375 4.20005C0.24375 4.20005 0 6.14536 0 8.08599V9.90474C0 11.8454 0.239062 13.7907 0.239062 13.7907C0.239062 13.7907 0.473437 15.4454 1.19062 16.1719C2.10469 17.1282 3.30469 17.0954 3.83906 17.1985C5.76094 17.3813 12 17.4375 12 17.4375C12 17.4375 17.0438 17.4282 20.4 17.1891C20.8688 17.1329 21.8906 17.1282 22.8047 16.1719C23.5266 15.4454 23.7609 13.7907 23.7609 13.7907C23.7609 13.7907 24 11.85 24 9.90474V8.08599C24 6.14536 23.7609 4.20005 23.7609 4.20005ZM9.52031 12.1125V5.36724L16.0031 8.75161L9.52031 12.1125Z"
        fill="#FD4561"></path>
    </symbol>
  </svg>



</body>

</html>